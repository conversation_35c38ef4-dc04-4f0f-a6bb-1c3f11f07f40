{"groups": [{"name": "novel.cors", "type": "io.github.xxyopen.novel.core.config.CorsProperties", "sourceType": "io.github.xxyopen.novel.core.config.CorsProperties"}, {"name": "novel.xss", "type": "io.github.xxyopen.novel.core.config.XssProperties", "sourceType": "io.github.xxyopen.novel.core.config.XssProperties"}, {"name": "spring.mail", "type": "io.github.xxyopen.novel.core.config.MailProperties", "sourceType": "io.github.xxyopen.novel.core.config.MailProperties"}], "properties": [{"name": "novel.cors.allow-origins", "type": "java.util.List<java.lang.String>", "sourceType": "io.github.xxyopen.novel.core.config.CorsProperties"}, {"name": "novel.xss.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "io.github.xxyopen.novel.core.config.XssProperties"}, {"name": "novel.xss.excludes", "type": "java.util.List<java.lang.String>", "sourceType": "io.github.xxyopen.novel.core.config.XssProperties"}, {"name": "spring.mail.nickname", "type": "java.lang.String", "sourceType": "io.github.xxyopen.novel.core.config.MailProperties"}, {"name": "spring.mail.username", "type": "java.lang.String", "sourceType": "io.github.xxyopen.novel.core.config.MailProperties"}], "hints": []}