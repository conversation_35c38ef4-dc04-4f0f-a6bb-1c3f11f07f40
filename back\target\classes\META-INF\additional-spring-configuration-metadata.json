{"properties": [{"name": "spring.elasticsearch.enabled", "description": "Whether enable elasticsearch or not.", "type": "java.lang.Bo<PERSON>an"}, {"defaultValue": false, "name": "spring.amqp.enabled", "description": "Whether enable amqp or not.", "type": "java.lang.Bo<PERSON>an"}, {"name": "xxl.job.enabled", "description": "Whether enable xxl-job or not.", "type": "java.lang.Bo<PERSON>an"}, {"name": "novel.jwt.secret", "type": "java.lang.String", "description": "JWT 密钥."}, {"name": "novel.xss.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否开启 XSS 过滤."}, {"name": "novel.xss.excludes", "type": "java.util.List<java.lang.String>", "description": "XSS 过滤排除链接."}, {"name": "novel.file.upload.path", "type": "java.lang.String", "description": "上传文件目录."}, {"name": "novel.cors.allow-origins", "type": "java.util.List<java.lang.String>", "description": "允许跨域的域名."}, {"name": "xxl.job.admin.addresses", "type": "java.lang.String", "description": "调度中心部署根地址."}, {"name": "xxl.job.executor.appname", "type": "java.lang.String", "description": "执行器 AppName."}, {"name": "xxl.job.executor.logpath", "type": "java.lang.String", "description": "执行器运行日志文件存储磁盘路径."}, {"name": "xxl.job.accessToken", "type": "java.lang.String", "description": "xxl-job accessToken."}, {"name": "spring.elasticsearch.ssl.verification-mode", "type": "java.lang.String", "description": "设置 ssl 的认证模式,如果该配置项为 none ,说明不需要认证,信任所有的 ssl 证书."}, {"defaultValue": true, "name": "spring.shardingsphere.enabled", "description": "Whether enable shardingsphere or not.", "type": "java.lang.Bo<PERSON>an"}]}