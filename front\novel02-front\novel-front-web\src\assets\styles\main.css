@charset "utf-8";
.items_txt .author a, .updateTable .author a { cursor: text }
.friend_link { padding: 12px 0 0; line-height: 2.4; text-align: center }
.friend_link a { margin: 0 10px; display: inline-block }
/*.leftBox, .rightBox, .rightBox2 { margin-bottom: 14px }
.channelBanner .leftBox, .channelBanner .rightBox { height: 334px; overflow: hidden }*/
.channelPic .leftBox, .channelPic .rightBox { /*height: 515px; */overflow: hidden }
.channelTable .leftBox { /*height: 1046px;*/ overflow: hidden }
.scBigImg img, .rightList li.on .cover img, .itemsList .items_img img { box-shadow: 0 0 1px rgba(0,0,0,.05) }
.scBigImg:hover img, .rightList li.on .cover a:hover img, .itemsList .items_img:hover img { box-shadow: 0 0 1px rgb(0,0,0,.25) }
.leftBox { width: 720px; float: left; /*border: 1px solid #EAEAEA*/ }
.sliderContent { width: 306px; float: left; /*margin: 16px 0 16px 14px;*/ position: relative }
.scSmallImg { position: absolute; top: 0px; right: 0px; /*height: 335px*/ }
.scSmallImg li { height: 65px; margin-bottom: 8px; border: 2px solid #fff }
.scSmallImg li.on { border: 2px solid #FF7800 }
.scSmallImg img { width: auto; height: 65px; cursor: pointer; filter: alpha(opacity=60); -moz-opacity: 0.6; opacity: 0.6 }
.scSmallImg li.on img { filter: alpha(opacity=100); -moz-opacity: 1; opacity: 1 }
.scBigImg dd { display: none }
.scBigImg dd.on { display: block }
.scBigImg img { width: 240px; height: 300px; background: #f6f6f6 }
.hot_articles { width: 396px; float: right; padding: 0 2px }
.hot_articles dl { padding: 0 4px 8px; border-bottom: 1px dotted #eae6e2 }
.hot_articles .hot_recommend { margin-bottom: 12px; }
.hot_articles dt { /*height: 40px; line-height: 40px;*/ padding-bottom: 7px; text-align: center; font-size: 16px; font-weight: 600; line-height: 1.8 }
.hot_articles dt a { color: #F70 }
.hot_articles dd { line-height: 30px; font-size: 14px; overflow: hidden }
.hot_articles dd a { white-space: nowrap; text-overflow: ellipsis; overflow: hidden; }
.hot_articles .hot_recommend dd a { width: 49%; padding-right: 1%; float: left; }
.hot_articles .hot_notice dd a { padding-right: 1%; }
.hot_articles span.tit { color: #f70; margin-right: 6px }
.hot_articles .hot_notice { border: none }
.hot_articles .line { padding: 0 14px; color: #eee }
.rightBox { width: 240px; float: right; /*border: 1px solid #EAEAEA;*/ position: relative }
.rightBox .title, .wrap_right_cont .title { /*height: 48px; margin: 0 14px;*/ border-bottom: 1px solid #e0e0e0 }
.rightBox .title h3, .wrap_right_cont .title h3 { line-height: 1; padding-bottom: 14px; display: inline-block; font-size: 20px; font-weight: 600; /*border-bottom: 4px solid transparent*/ }
/*.rightBox .title h3.on { border-color: #f80 }*/
.rightList ul { padding: 0  }
.rightList li { /*border-bottom: 1px dotted #e0e0e0; height: 37px; line-height: 37px;*/ overflow: hidden; position: relative; vertical-align: middle }
.rightList li:last-child { border: none }
.rightList .book_name { font-size: 14px; height: 34px; line-height: 34px; white-space: nowrap; text-overflow: ellipsis; overflow: hidden }
.rightList .book_intro { background: #f7f7f7; border: 1px solid #eee; clear: both; padding: 8px; word-break: break-all; zoom: 1; overflow: hidden; display: none }
.rightList .cover, .rightList .book_intro .txt { display: none }
.rightList li.on { height: auto; padding: 4px 0; border: none }
.rightList li.on .book_intro { display: block }
.rightList li.on .cover { float: left; display: block }
.rightList li.on .cover img { width: 60px; height: auto; background: #f6f6f6; margin-right: 9px }
.rightList li.on .book_intro .name { line-height: 26px; height: 26px; display: block; overflow: hidden }
.rightList_nobor ul { padding: 4px 14px 10px }
.rightList_nobor li { height: auto; padding: 10px 0!important; border: none }
.book_intro .author { color: #999; display: block; line-height: 30px }
.book_intro .class { color: #999; display: block; line-height: 1 }
.rightList .on .book_intro .txt { height: 72px; line-height: 1.5; color: #808080; overflow: hidden; display: block }

.rightList li i, .rankTable .rank i { width: 17px; height: 17px; line-height: 17px; text-align: center; background-color: #999; color: #fff; vertical-align: middle; display: inline-block; font-size: 12px; }
.rightList li i { float: left; margin: 8px 7px 0 0; }
.rankTable .rank i { margin: 1px 1px 0 }
/*.rightList li.on i { position: absolute; top: 12px; left: 0; margin: 0; display:none }*/
.rightList li.num1 i, .rankTable .rank .num1 { background-color: #fc7403 }
.rightList li.num2 i, .rankTable .rank .num2 { background-color: #f79415 }
.rightList li.num3 i, .rankTable .rank .num3 { background-color: #ffa95e }
.rightList li.num1 i,.rightList li.num2 i,.rightList li.num3 i { display:block }
/*.rightList .more{ margin: 1px 0; height: 34px; line-height: 34px; border-radius: 1px; background-color: #f7f7f7; text-align: center }
.rightList .more a{ display: block; color: #666 }*/
.leftBox .title { border-bottom: 1px solid #e9e9e9 }
.leftBox .title h2 { line-height: 1; padding-bottom: 14px; display: inline-block; font-size: 20px; font-weight: 600; /*border-bottom: 4px solid transparent*/ }
.picRecommend { width: 720px; padding: 12px 0 0 }
.itemsList { width: 50%; float: left; padding: 17px 0 }
.itemsList .items_img { float: left; margin-right: 14px }
.itemsList .items_img img { width: 96px; height: 120px; background: #f6f6f6 }
.items_txt { width: 230px; float: left; /*padding-right: 20px;*/ }
.items_txt h4 { height: 20px; line-height: 20px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; word-break: keep-all; margin-bottom: 8px; font-size: 16px; font-weight: normal }
.items_txt .author { margin: 8px 0 }
.items_txt .author a { color: #a6a6a6 }
.items_txt .intro { margin-top: 8px; line-height: 1.5; height: 54px; overflow: hidden }
.searchTipBar { color: #333; font-size: 14px; padding: 1px 7px 16px 7px }
.leftBox .updateTable { width: 718px; }
.updateTable { color: #999 }
.updateTable table { width: 100%; margin-bottom: 14px; }
.updateTable th, .updateTable td { height: 41px; line-height: 41px; vertical-align: middle; padding-left: 1px; text-align: left }
.updateTable th { font-weight: normal; font-size: 14px; }
.updateTable td { border-top: 1px solid #eee }
.updateTable .style { width: 74px; font-size: 14px; }
.updateTable .name { width: 192px; padding-right: 10px; font-size: 14px; }
.updateTable td.name { padding-top: 15px; }
.updateTable .name a, .updateTable .chapter a { max-width: 168px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; word-break: keep-all }
.updateTable .chapter { padding-right: 5px }
.updateTable .chapter a { max-width: 200px; float: left; color: #666 }
.updateTable .author { width: 82px; text-align: left }
.updateTable td.author { padding-top: 15px; }
.updateTable .time { width: 82px; text-align: center }
.updateTable .word { width: 60px; padding-right: 10px; text-align: right }
.updateTable .rank { width: 2.5em; padding-right: 10px; text-align: center }
.updateTable .name a, .updateTable .chapter a, .updateTable .author a { height: 41px; line-height: 41px; display: inline-block; overflow: hidden }
.rankBox { padding-bottom: 14px; height: auto!important }
.rankTable th { background: #f9f9f9; color: #333 }
.rankTable td { border: none; height: 40px; line-height: 40px }
.rankTable tr:nth-child(2n) td { background: #fafafa }
.rankTable .chapter a { max-width: 176px }
.classTable { font-size: 14px }
.classTable .rank { width: 60px; }
.classTable .rank i { float: inherit; margin: 0; color: #fff }
.classTable .style { width: 100px; }
.classTable .name { width: 250px; }
.classTable .name a, .classTable .chapter a { max-width: 90% }
.classTable .author { width: 120px }
.classTable .word { width: 80px; padding-right: 15px }
.rightBox2 { width: 266px; float: right; border: 1px solid #EAEAEA; position: relative; overflow: hidden }
.rightBox2 .title h3 { height: 45px; line-height: 48px; padding: 0 30px; font-size: 18px; font-weight: normal; color: #ff758f; border-bottom: 4px solid #ff758f }
.rightList2 li { vertical-align: middle }
.rightList2 li a { display: block; /*padding: 0 30px;*/ height: 47px; line-height: 47px; font-size: 16px; overflow: hidden; border-top: 1px dotted #eee; }
.rightList2 li:first-child a { border: none }
.rightList2 li a.on, .rightList2 li a:hover { color: #f70 }
.so_tag { /*padding: 4px 14px 0;*/  font-size: 14px; padding: 5px 0 }
.so_tag li { padding: 0 0 24px; /*border-bottom: 1px solid #eee*/ }
.so_tag li:last-child { padding: 0 0 4px }
.so_tag li .tit, .so_tag li a { line-height: 1; padding: 3px 7px; margin-right: 12px }
.so_tag li .tit { color: #999 }
.so_tag li a.on, .so_tag li a:hover { color: #f70 }
.so_tag li .so_girl { display: inline-block }
.so_tag li .so_boy { display: inline-block/*; margin: 8px 0 0 140px;*/ }

/*.payBox { width: 998px; border: 1px solid #eaeaea }*/
.payHead { height: 36px; line-height: 36px; padding: 20px 0 30px; margin: 0 24px; font-size: 16px; border-bottom: 1px solid #eaeaea }
.payHead .user_name { margin-right: 25px }
.payHead .btn_gray { font-size: 14px; padding: 10px 20px; margin-left: 20px }
.payFoot { line-height: 2.4; padding: 30px 0 40px; margin: 0 24px; font-size: 13px; color: #999; border-top: 1px solid #eee; }
.payCon { margin: 0 24px }
.payCon h5 { font-size: 16px; font-weight: normal; padding: 28px 0 2px }
.pay_way { padding-bottom: 5px }
.pay_way li { width: 196px; text-align: center; border: 2px solid #eee; border-radius: 4px; margin: 20px 26px 3px 0; float: left; cursor: pointer; line-height: 1 }
.pay_way li.on { border-color: #f80 }
.pay_way li .pay_pic { width: 180px; margin: 12px auto; }
.pay_way li strong { font-size: 24px; display: block; line-height: 1; padding: 20px 0 5px }
.pay_way li .pay_mn { display: table-cell; width: 196px; height: 40px; vertical-align: middle; line-height: 1.2; padding-bottom: 12px; font-size: 14px; text-align: center }
.pay_way li .pay_mn em.red { display: block }
.pay_Checkout { padding: 20px 0; font-size: 14px; line-height: 1.8; }
.pay_Checkout .btn_red { margin: 20px 0; }

.payResultBox { padding: 90px 40px 160px; text-align: center }
.payResultBox h3 { font-size: 38px; line-height: 1; padding-bottom: 30px; }
.payResultBox .list { display: inline-block; padding-bottom: 15px;}
.payResultBox .list li { font-size: 16px; line-height: 36px }
.payResultImg { width: 60px;
    margin-right: 12px;
    vertical-align: middle; }
/*.bookCover, .reply_bar { padding: 14px }*/
.bookCover .book_cover { width: 200px; display: block; height: auto; margin-right: 25px; float: left; position: relative; overflow: hidden; box-shadow: 0 1px 6px rgba(0,0,0,.3), 0 0 5px #f9f2e9 inset; transition: color .3s,background-color .3s,border .3s;
}
.bookCover .cover { width: 100%; height: 100%; background: #f6f6f6;
	-webkit-transition: -webkit-transform .3s ease-out;
  -moz-transition: -moz-transform .3s ease-out;
  -ms-transition: -ms-transform .3s ease-out;
  transition: transform .3s ease-out;
}
.bookCover .cover:hover {
  -webkit-transform: scale(1.05);
  -moz-transform: scale(1.05);
  -o-transform: scale(1.05);
  transform: scale(1.05) }
.book_info { width: 755px; float: left }
.book_info h1 { font-size: 25px; display: inline-block; line-height: 1; }
.book_info .author { font-size: 14px; margin-left: 20px; color: #444 }
.book_info .list { padding: 15px 0 20px }
.book_info .list li { line-height: 26px; color: #666 }
.book_info .list li .item { width: 20%; display: inline-block }
/*目录页*/
.book_info1 { text-align: center; padding: 10px 0 15px }
.book_info1 .tit { padding: 10px 0 20px }
.book_info1 h1 { font-size: 28px; display: inline-block }
.book_info1 .list { padding: 5px 0; font-size: 14px }
.book_info1 .list li { line-height: 26px; color: #999 }
.book_info1 .list li span { display: inline-block; margin: 0 15px }
.dirWrap { padding-bottom: 30px }
.dirWrap h3 { padding-left: 6px; font-size: 14px; background: #f9f9f9; height: 40px; line-height: 40px; font-weight: normal; position: relative; cursor: pointer; margin: 0 0 5px; border-radius: 3px }
.dirList { overflow: hidden; padding-bottom: 20px }
.dirList li { float: left; width: 265px; padding-left: 5px; padding-right: 30px; height: 40px; line-height: 40px; overflow: hidden; border-bottom: 1px dotted #ddd; *zoom:1; font-size: 14px
}
.dirList li a { float: left; text-overflow: ellipsis; overflow: hidden; white-space: nowrap }
.dirList li i.red { padding-left: 5px }
.book_info .intro_txt { height: 96px; min-height: 96px; line-height: 24px; font-size: 14px; position: relative; margin-bottom: 26px; overflow: hidden }
.book_info .intro_txt em.black9 { font-weight: bold; color: #333; display: block; }
/*.book_info .intro_txt p { text-indent:2em }*/
.icon_show, .icon_hide { display:inline-block; color:#2972cc; height: 24px; padding:0 2px 0 10px; text-indent: 0; text-align: center; font-size: 12px; position: absolute; right: 0; bottom: 0; background: #fff }
.icon_show i, .icon_hide i { display:inline-block; width:12px; height:12px; background:url(../images/icon_dt.png) no-repeat 0 2px; margin-right: 4px; *vertical-align:middle }
.icon_hide i { background-position:-12px 2px }
.icon_hide { display: none }
.btns .btn_red, .btns .btn_ora, .btns .btn_addsj { margin-right: 24px }
.book_tit { /*height: 48px; line-height: 48px; margin: 0 14px;*/ border-bottom: 1px solid #eee; overflow: hidden; padding-bottom: 14px; line-height: 1.2 }
.book_tit .fl { font-size: 14px; color: #666 }
.book_tit .fl h3 { font-size: 20px; color: #333; margin-right: 5px; display: inline }
.book_tit .fr { font-size: 13px }
.bookChapter .list { padding: 8px 0 }
.bookChapter .list li { line-height: 36px; overflow: hidden }
.zj_yl { color: #999; font-size: 13px }
/*.bookChapter .list li .zj { width: 50%; float: left }
.bookChapter .list li .zj_1 a { color: #f60 }*/



/*.commentBar { padding: 0 14px }*/
.comment_list { padding: 20px 0; border-bottom: 1px solid #eee }
.comment_list:last-child { border: none }
.comment_list .user_heads { /*width: 54px; height: 54px; float: left;*/ position:relative; margin-right: 20px }
.comment_list .user_head { width: 50px; height: 50px; border-radius: 50%; background: #f6f6f6 }
.comment_list .user_heads span { display: block; margin: 0; position: absolute; left: 12px; bottom: 0 }
.comment_list ul { width: 640px }
.comment_list .li_0 { font-family: "宋体" }
.comment_list .li_0 strong { font-size: 14px; color: #f00 }
.comment_list .li_1 { overflow: hidden }
.comment_list .user_name { color: #ed4259 }
.comment_list .li_2 { padding: 6px 0 }
.comment_list .li_3 { color: #999 }
.comment_list .reply { padding-left: 12px }
.comment_list .num { color: #ed4259; margin: 0 3px }
.comment_list .li_4 { line-height: 34px; padding-top: 8px; margin-top: 15px; border-top: 1px solid #eaeaea }
.no_comment { padding: 70px 14px 115px; color: #CCCCCC; text-align: center; font-size: 14px; }
.pl_bar li { display: block }
.pl_bar .name { color: #666; padding-top: 2px; font-size: 14px }
.pl_bar .dec { font-size: 14px; line-height: 1.8; padding: 12px 0 }
.pl_bar .other { line-height: 24px; color: #999; font-size: 13px }
.pl_bar .other a { display: inline-block; color: #999 }
.pl_bar .reply { padding-left: 22px; background: url(../images/icon_reply.png) no-repeat 0 2px }
.reply_bar .tit { line-height: 52px; font-size: 13px }
.replay_text { width: 100%; height: 110px; border: 1px solid #eaeaea; border-radius: 5px; padding: 10px; box-sizing: border-box; font-size: 14px; box-shadow: 0 0 4px 2px hsla(0,0%,92%,.35); }
.replay_text:hover { background: #fff }
.reply_btn { padding: 17px 0 19px; overflow: hidden }
.reply_bar .reply_btn { padding-bottom: 4px }
.reply_btn .btn_red { padding: 10px 20px; font-size: 14px }
.reply_btn .fr { margin-top: 8px }
.write_bar { padding: 1rem 0; margin: 0 1rem }
.write_comment { padding: 1rem; background: #f6f6f6; min-height: 16rem }
.write_comment .text { width: 100%; min-height: 10rem; border: 1px solid #ddd; font-size: 0.875rem; line-height: 1.8; margin-bottom: 1rem }
.book_comment_tit { font-size: 24px; padding: 20px 15px 10px 15px }
.page_bar { padding: 1rem 0; margin: 0 1rem; border-top: 1px solid #eee }
.page_bar li { width: 33.3%; float: left; text-align: center }
.page_bar li a, .page_bar li .select_page { display: block; height: 2rem; line-height: 2rem; font-size: 0.875rem; border: 1px solid #eee; background: #fff; box-sizing: border-box }
.page_bar .previous a { margin-right: 1rem }
.page_bar .next a { margin-left: 1rem }
.page_bar li .select_page { width: 100% }
.icon_jh, .icon_zd { text-align: center; margin: 2px 5px 0 0; color: #fff; font-size: 12px; padding: 3px 3px; line-height: 1; display: inline-block; background: #ed4259; border-radius: 2px }
.icon_zd { background: #4a90e2 }


.hot_notice span, .items_txt .intro a, .updateTable .author a, .updateTable .style a, .updateTable .time a, .updateTable th { color: #888 }
.items_txt .intro a:hover, .rightList .more a:hover, .updateTable .style a:hover, .rightList .on .book_intro .txt:hover { color: #f70 }
.icon_show:hover, .icon_hide:hover { color: #2972cc }
.channelChapterlist { min-height: 600px }
