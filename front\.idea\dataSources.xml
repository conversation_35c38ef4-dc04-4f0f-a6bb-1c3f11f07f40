<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="novel@localhost" uuid="79222d5a-9fd2-46b5-bcf3-c8355416d730">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <imported>true</imported>
      <remarks>$PROJECT_DIR$/novel02-back/src/main/resources/application.yml</remarks>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>*******************************************************************************************************************************</jdbc-url>
      <jdbc-additional-properties>
        <property name="com.intellij.clouds.kubernetes.db.host.port" />
        <property name="com.intellij.clouds.kubernetes.db.enabled" value="false" />
        <property name="com.intellij.clouds.kubernetes.db.container.port" />
      </jdbc-additional-properties>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="0@127.0.0.1" uuid="8f444ce0-5751-4d0f-9b23-1e3d7d6f0e06">
      <driver-ref>redis</driver-ref>
      <synchronize>true</synchronize>
      <imported>true</imported>
      <remarks>$PROJECT_DIR$/novel02-back/src/main/resources/application.yml</remarks>
      <jdbc-driver>jdbc.RedisDriver</jdbc-driver>
      <jdbc-url>*****************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>