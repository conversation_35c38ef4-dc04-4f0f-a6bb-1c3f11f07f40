<!--
 <template>
  <div class="mainNav" id="mainNav">
    <div class="box_center cf">
      <ul class="nav" id="navModule">
        <li><router-link :to="{ name: 'home' }">首页</router-link></li>
        <li>
          <router-link :to="{ name: 'bookclass' }"> 全部作品 </router-link>
        </li>
        <li><router-link :to="{ name: 'bookRank' }">排行榜</router-link></li>
        &lt;!&ndash;<li class=""><a href="/pay/index.html">充值</a></li>&ndash;&gt;
        <li><a @click="goAuthor" href="javascript:void(0)">作家专区</a></li>
      </ul>
    </div>
  </div>
</template>

<script>
import { reactive, toRefs, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getToken} from "@/utils/auth";
import {getAuthorStatus} from "@/api/author"
export default {
  name: "Navbar",
  setup() {
    const route = useRoute();
    const router = useRouter();
    const goAuthor = async () => {
      if (!getToken()) {
        router.push({
          name: "login",
        });
        return;
      }

      const {data} = await getAuthorStatus();
      if(data === null){
          router.push({
          name: "authorRegister",
        });
        return;
      }

      let routeUrl = router.resolve({
        name: "authorBookList",
      });
      window.open(routeUrl.href, "_blank");
    };
    return {
      goAuthor,
    };
  },
};
</script>-->
<template>
  <div class="mainNav" id="mainNav">
    <div class="box_center cf">
      <ul class="nav" id="navModule">
        <li>
          <router-link
              :to="{ name: 'home' }"
              class="nav-link"
              exact-active-class="router-link-active"
          >首页</router-link>
        </li>
        <li>
          <router-link
              :to="{ name: 'bookclass' }"
              class="nav-link"
              exact-active-class="router-link-active"
          >全部作品</router-link>
        </li>
        <li>
          <router-link
              :to="{ name: 'bookRank' }"
              class="nav-link"
              exact-active-class="router-link-active"
          >排行榜</router-link>
        </li>
        <li>
          <a @click="goAuthor" class="nav-link author-link" href="javascript:void(0)">作家专区</a>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getToken } from "@/utils/auth";
import { getAuthorStatus } from "@/api/author";

export default {
  name: "Navbar",
  setup() {
    const route = useRoute();
    const router = useRouter();

    const goAuthor = async () => {
      if (!getToken()) {
        router.push({ name: "login" });
        return;
      }

      try {
        const { data } = await getAuthorStatus();
        if(!data) {
          router.push({ name: "authorRegister" });
          return;
        }

        const routeUrl = router.resolve({ name: "authorBookList" });
        window.open(routeUrl.href, "_blank");
      } catch (error) {
        console.error("获取作者状态失败:", error);
      }
    };

    // 初始化导航栏动画
    onMounted(() => {
      const navLinks = document.querySelectorAll('.nav-link');
      navLinks.forEach(link => {
        link.addEventListener('mouseenter', () => {
          link.style.transform = 'translateY(-2px)';
        });
        link.addEventListener('mouseleave', () => {
          link.style.transform = 'translateY(0)';
        });
      });
    });

    return { goAuthor };
  },
};
</script>

<style scoped>
.mainNav {
  background: linear-gradient(135deg, #d3e097 0%, #c2650e 100%);
  box-shadow: 0 2px 15px rgba(0,0,0,0.1);
  padding: 1rem 0;
  position: relative;
  z-index: 1000;
}

.nav {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin: 0;
  padding: 0;
}

.nav li {
  list-style: none;
  position: relative;
}

.nav-link {
  color: rgba(255,255,255,0.9);
  text-decoration: none;
  font-size: 1.1rem;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
  display: block;
  position: relative;
}

.nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 50%;
  background: #fff;
  transition: all 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
  left: 0;
}

.nav-link:hover {
  color: #fff;
  transform: translateY(-2px);
}

.author-link {
  background: rgba(255,255,255,0.1);
  border-radius: 4px;
  padding: 0.5rem 1.5rem !important;
}

.author-link:hover {
  background: rgba(255,255,255,0.2);
}

.router-link-active {
  color: #fff !important;
  font-weight: 500;
  transform: translateY(-2px);
}

.router-link-active::after {
  width: 100% !important;
  left: 0 !important;
  background: #fff;
}

/* 添加精确激活状态 */
.router-link-exact-active {
  color: #ffd700 !important; /* 金色高亮 */
}

.router-link-exact-active::after {
  background: #ffd700; /* 金色下划线 */
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.mainNav {
  animation: fadeIn 0.5s ease-out;
}
</style>