<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="novel-admin" />
        <module name="novel-common" />
        <module name="novel-crawl" />
        <module name="novel-front" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="novel-admin" options="-parameters" />
      <module name="novel-common" options="-parameters" />
      <module name="novel-crawl" options="-parameters" />
      <module name="novel-front" options="-parameters" />
    </option>
  </component>
</project>